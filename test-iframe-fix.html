<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe工具栏位置修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            padding-top: 50px;
        }
        .test-section {
            background-color: #f0f0f0;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .iframe-container {
            border: 3px solid #333;
            margin: 20px 0;
            padding: 15px;
            background-color: #fafafa;
        }
        iframe {
            width: 100%;
            height: 250px;
            border: 2px solid #666;
        }
        .layer-info {
            background-color: #e8f4fd;
            padding: 12px;
            margin: 10px 0;
            border-left: 5px solid #2196F3;
            font-weight: bold;
        }
        .fix-info {
            background-color: #d4edda;
            padding: 15px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
            border-radius: 5px;
        }
        .highlight-text {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>🔧 iframe工具栏位置修复测试</h1>
    
    <div class="fix-info">
        <h3>修复内容</h3>
        <p><strong>问题</strong>：工具栏位置多出约45像素偏移，遮挡选中文字</p>
        <p><strong>原因</strong>：重复计算滚动偏移</p>
        <p><strong>修复</strong>：移除重复的滚动偏移计算</p>
    </div>
    
    <div class="test-section">
        <div class="layer-info">当前层级：顶层页面</div>
        <div class="highlight-text">
            请选中这段文字测试工具栏位置。
        </div>
    </div>
    
    <div class="iframe-container">
        <h3>第三层iframe测试（关键）</h3>
        <iframe src="data:text/html;charset=utf-8,
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; padding-top: 50px; }
                .highlight-text { background-color: #d4edda; padding: 20px; margin: 20px 0; border-radius: 8px; font-size: 18px; border: 2px solid #28a745; }
                .layer-info { background-color: #f8d7da; padding: 12px; margin: 10px 0; border-left: 5px solid #dc3545; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='layer-info'>第三层iframe - 关键测试层级</div>
            <div class='highlight-text'>
                🎯 请选中这段文字！修复前工具栏会遮挡文字（多出45px），修复后应该正确显示在上方。
            </div>
            <div class='highlight-text'>
                📝 这是第二段测试文字，用于验证修复效果的一致性。
            </div>
        </body>
        </html>"></iframe>
    </div>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>重点测试第三层iframe中的文字选择，观察工具栏是否正确显示在文字上方而不是遮挡文字。</p>
    </div>
</body>
</html>
